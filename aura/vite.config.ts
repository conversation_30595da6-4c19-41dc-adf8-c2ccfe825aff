import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig, type UserConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

// Related: https://github.com/remix-run/remix/issues/2835#issuecomment-1144102176
// Replace the HOST env var with SHOPIFY_APP_URL so that it doesn't break the remix server. The CLI will eventually
// stop passing in HOST, so we can remove this workaround after the next major release.
if (
  process.env.HOST &&
  (!process.env.SHOPIFY_APP_URL ||
    process.env.SHOPIFY_APP_URL === process.env.HOST)
) {
  process.env.SHOPIFY_APP_URL = process.env.HOST;
  delete process.env.HOST;
}

const host = new URL(process.env.SHOPIFY_APP_URL || "http://localhost")
  .hostname;

// Set up consistent HMR configuration 
let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "localhost",
    port: 54030, // Use fixed port for HMR to avoid random port assignment
    clientPort: 54030,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    host: host,
    port: parseInt(process.env.FRONTEND_PORT!) || 8002,
    clientPort: 443,
  };
}

export default defineConfig({
  server: {
    allowedHosts: [host, "aura.fy.studio"], // Added aura.fy.studio to allowed hosts
    cors: {
      preflightContinue: true,
    },
    port: 54029, // Hardcoded port for consistent access
    host: "localhost", // Force IPv4 only to avoid IPv6 issues
    hmr: {
      ...hmrConfig,
      host: "localhost", // Force IPv4 for HMR as well
    },
    fs: {
      // See https://vitejs.dev/config/server-options.html#server-fs-allow for more information
      allow: ["app", "node_modules"],
    },
  },
  plugins: [
    remix({
      ignoredRouteFiles: ["**/.*"],
      serverBuildFile: "index.js",
      serverEntryPoint: "./app/entry.server.cloudflare.tsx",
      serverPlatform: "neutral",
      serverMinify: false,
      serverModuleFormat: "esm",
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_lazyRouteDiscovery: true,
        v3_singleFetch: false,
        v3_routeConfig: true,
      },
    }),
    tsconfigPaths(),
  ],
  build: {
    assetsInlineLimit: 0,
    cssCodeSplit: false,
  },
  ssr: {
    resolve: {
      conditions: ["workerd", "worker", "browser"],
    },
    target: "webworker",
    noExternal: ["@shopify/polaris", "@shopify/app-bridge-react"],
    external: ["@remix-run/node"],
  },
  resolve: {
    mainFields: ["browser", "module", "main"],
  },
  optimizeDeps: {
    include: ["@shopify/app-bridge-react", "@shopify/polaris"],
  },
}) satisfies UserConfig;
