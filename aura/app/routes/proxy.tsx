import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/cloudflare";
import { authenticate } from "../shopify.server";
import { Outlet } from "@remix-run/react";

/**
 * App Proxy endpoint for processing voice assistant audio
 * This is accessed through the Shopify App Proxy at: /apps/voice
 * 
 * The app_proxy configuration in shopify.app.toml is:
 * [app_proxy]
 * url = "https://aura.fy.studio/proxy"
 * prefix = "apps"
 * subpath = "voice"
 */

// GET handler for options/health checks
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const requestId = `proxy-main-${Date.now()}`;
  try {
    const { liquid, session } = await authenticate.public.appProxy(request);
    
    // Get request details
    const url = new URL(request.url);
    const path = url.pathname;
    
    // Log all request details for debugging
    console.log(`===== [${requestId}] PROXY REQUEST DETAILS =====`);
    console.log(`[${requestId}] Request URL:`, url.toString());
    console.log(`[${requestId}] Request path:`, path);
    console.log(`[${requestId}] Session shop:`, session?.shop);
    console.log(`[${requestId}] Query parameters:`, Object.fromEntries([...url.searchParams.entries()].map(([k, v]) => [k, v])));
    console.log(`[${requestId}] ==============================`);
    
    // Handle SSE connections via /apps/voice?stream=true
    // If this is a preflight OPTIONS request, handle it with proper CORS headers
    if (request.method === "OPTIONS") {
      return new Response(null, {
        status: 204,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Accept",
          "Access-Control-Max-Age": "86400"
        }
      });
    }
    
    // Enhanced request logging for debug
    console.log(`[${requestId}] Complete request details:`);
    console.log(`[${requestId}] - Headers:`, Object.fromEntries([...request.headers.entries()].map(([k, v]) => [k, v])));
    console.log(`[${requestId}] - Method:`, request.method);
    
    // Check for Shopify App Proxy signature parameters
    const hasSignature = url.searchParams.has('signature');
    const hasTimestamp = url.searchParams.has('timestamp');
    const hasShop = url.searchParams.has('shop');
    console.log(`[${requestId}] Shopify signature present: ${hasSignature}`);
    console.log(`[${requestId}] Shopify timestamp present: ${hasTimestamp}`);
    console.log(`[${requestId}] Shop parameter present: ${hasShop}`);
    
    // Check if this is a direct API request handled by a nested route
    // This is important: Remix handles nested routes automatically, so we should
    // NOT attempt to manually handle requests for endpoints that have dedicated files
    const isApiRequest = path.includes('/api/') || path.endsWith('/api');
    const isTokenRequest = path.includes('/livekit/token');
    const isBotRequest = path.includes('/livekit/bot');
    
    if (isApiRequest) {
      console.log(`[${requestId}] ✅ Detected API request: ${path}`);
      console.log(`[${requestId}] - Token request: ${isTokenRequest}`);
      console.log(`[${requestId}] - Bot request: ${isBotRequest}`);
      console.log(`[${requestId}] This request will be handled by the nested route structure.`);
      
      // Let Remix's nested routing handle this request
      return liquid(`Voice Assistant API request received. Forwarding to handler for: ${path}`, { layout: false });
    }
    
    // If not an API request, return a simple health check response
    console.log(`[${requestId}] Standard proxy route request, returning health check response.`);
    
    // Return a simple liquid response for health check
    return liquid("Voice Assistant API is running", { layout: false });
  } catch (error) {
    console.error(`[${requestId}] Error in proxy route:`, error);
    return json({ 
      error: "Proxy authentication error",
      message: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 401 });
  }
};

// Outlet is required for nested routes
export default function ProxyLayout() {
  return <Outlet />;
}

// POST handler for processing audio - the main voice assistant integration point
export const action = async ({ request }: ActionFunctionArgs) => {
  const requestId = `proxy-audio-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  try {
    // Use Shopify's authentication for app proxy
    const { session } = await authenticate.public.appProxy(request);
    
    // Enhanced request logging for debugging
    console.log(`===== [${requestId}] VOICE ASSISTANT AUDIO REQUEST =====`);
    console.log(`[${requestId}] Request URL:`, request.url);
    console.log(`[${requestId}] Request path:`, new URL(request.url).pathname);
    console.log(`[${requestId}] Session shop:`, session?.shop);
    console.log(`[${requestId}] Headers:`, Object.fromEntries([...request.headers.entries()].map(([k, v]) => [k, v])));
    console.log(`[${requestId}] ==============================`);
    
    if (request.method !== "POST") {
      console.log(`[${requestId}] Method not allowed:`, request.method);
      return json({ error: "Method not allowed" }, { status: 405 });
    }
    
    // Check if this is a direct API request that should be handled by a nested route
    const path = new URL(request.url).pathname;
    if (path.includes('/api/')) {
      console.log(`[${requestId}] ✅ API POST request detected: ${path}`);
      console.log(`[${requestId}] This request will be handled by the nested route structure.`);
      // Let Remix's nested routing handle this request - should not execute this code
      return json({ error: "Invalid path for main proxy handler" }, { status: 400 });
    }
    
    // Define consistent CORS headers for all responses
    const CORS_HEADERS = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Accept",
    };
    
    try {
      // Parse the JSON body to get audio data
      const body = await request.json();
      const { audio, shopDomain, command, requestId: bodyRequestId, sessionId } = body;
      
      console.log(`[${requestId}] Request received with data:`);
      console.log(`[${requestId}] - Shop domain:`, shopDomain);
      console.log(`[${requestId}] - Command provided:`, !!command);
      console.log(`[${requestId}] - Audio data provided:`, !!audio);
      console.log(`[${requestId}] - Body Request ID:`, bodyRequestId);
      console.log(`[${requestId}] - Session ID:`, sessionId);
      
      // Support both audio data and text commands
      if (!audio && !command) {
        return json({ 
          error: "Missing audio data or command",
          requestId
        }, { 
          status: 400,
          headers: CORS_HEADERS 
        });
      }
      
      // Use shop from session if shopDomain isn't provided
      const shop = shopDomain || session?.shop;
      if (!shop) {
        console.error(`[${requestId}] No shop domain provided and no session shop found`);
        return json({ 
          error: "Missing shop domain",
          requestId 
        }, { 
          status: 400,
          headers: CORS_HEADERS 
        });
      }
      
      // Check if we have a valid session ID from an SSE connection
      if (sessionId) {
        console.log(`[${requestId}] Request associated with SSE session: ${sessionId}`);
      }
      
      // Generate a unique tracking ID for this request if not provided
      const uniqueRequestId = bodyRequestId || requestId;
      console.log(`[${requestId}] Using request ID: ${uniqueRequestId}`);
      
      // Process the command or audio data using LiveKit proxy
      console.log(`[${requestId}] Processing audio via LiveKit Proxy (HTTP POST)`);
      const livekitProxyUrl = process.env.LIVEKIT_PROXY_URL || "http://localhost:7880"; // Use HTTP URL
      
      console.log(`[${requestId}] Sending POST request to LiveKit Proxy at: ${livekitProxyUrl}`);
      
      try {
        const proxyResponse = await fetch(livekitProxyUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            // Forward relevant headers from the original request
            'X-Shopify-Shop-Domain': shop,
            'X-Request-ID': uniqueRequestId,
            'X-Session-ID': sessionId || '', // Send session ID if available
          },
          body: JSON.stringify({
            audio: audio, // Send the audio data
            command: command, // Send command if present
            shopId: shop, // Send shop domain
            sessionId: sessionId, // Send session ID
            requestId: uniqueRequestId // Send request ID
          }),
        });
        
        if (!proxyResponse.ok) {
          // Handle non-2xx responses from the proxy
          const errorText = await proxyResponse.text();
          console.error(`[${requestId}] Error response from LiveKit Proxy: ${proxyResponse.status} - ${errorText}`);
          return json({ 
            error: 'Error communicating with voice service',
            message: `Proxy returned status ${proxyResponse.status}`,
            requestId
          }, { 
            status: 502, // Bad Gateway might be appropriate
            headers: CORS_HEADERS 
          });
        }
        
        // Proxy likely returns 202 Accepted or similar if successful
        // The actual result will come via SSE later
        console.log(`[${requestId}] LiveKit Proxy responded with status: ${proxyResponse.status}`);
        const responseBody = await proxyResponse.json(); // Assuming proxy sends back JSON ack
        
        return json({ 
          status: 'processing', 
          message: 'Audio received, processing...',
          proxyResponse: responseBody, // Include proxy ack if needed
          requestId
        }, { 
          status: 202, // Accepted
          headers: CORS_HEADERS 
        });
        
      } catch (fetchError) {
        // Type check for the caught error
        let errorMessage = 'An unknown error occurred while connecting to the voice service.';
        if (fetchError instanceof Error) {
          errorMessage = fetchError.message;
        } else if (typeof fetchError === 'string') {
          errorMessage = fetchError;
        }
        console.error(`[${requestId}] Error sending POST request to LiveKit Proxy:`, fetchError);
        return json({ 
          error: 'Failed to connect to voice service',
          message: errorMessage,
          requestId
        }, { 
          status: 503, // Service Unavailable
          headers: CORS_HEADERS 
        });
      }
    } catch (parseError) {
      console.error(`[${requestId}] Error parsing request body:`, parseError);
      return json({ 
        error: 'Error parsing request body',
        message: 'The request body could not be parsed as valid JSON',
        requestId
      }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
  } catch (error) {
    console.error(`[${requestId}] Error in proxy action:`, error);
    return json({ 
      error: 'Proxy error',
      message: error instanceof Error ? error.message : 'An unknown error occurred',
      requestId
    }, { 
      status: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Accept",
      }
    });
  }
};