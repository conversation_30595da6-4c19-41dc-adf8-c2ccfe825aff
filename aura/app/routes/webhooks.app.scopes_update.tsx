import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { authenticate, sessionStorage } from "../shopify.server";

export const action = async ({ request }: ActionFunctionArgs) => {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    console.log(`Received ${topic} webhook for ${shop}`);

    const current = payload.current as string[];
    if (session) {
        // Update session with new scopes using CloudflareKVSessionStorage
        const updatedSession = {
            ...session,
            scope: current.toString(),
        };
        
        // Save the updated session back to KV storage
        await sessionStorage.storeSession(updatedSession);
    }
    return new Response();
};
