import { LoaderFunctionArgs, ActionFunctionArgs, json } from "@remix-run/cloudflare";
import { useLoaderData, useActionData, Form } from "@remix-run/react";
import { authenticate } from "../shopify.server";

/**
 * Test route to manually trigger Storefront token generation
 */
async function triggerStorefrontTokenGeneration(shop: string, accessToken: string) {
  try {
    const backendWorkerUrl = process.env.BACKEND_WORKER_URL || 'https://aura-backend-worker.feisty-agency.workers.dev';

    console.log('[TEST] Triggering token generation for:', shop);
    console.log('[TEST] Backend worker URL:', backendWorkerUrl);
    console.log('[TEST] Access token (first 20 chars):', accessToken.substring(0, 20) + '...');

    const response = await fetch(`${backendWorkerUrl}/api/shopify/install`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shop,
        accessToken,
        scopes: process.env.SCOPES?.split(",") || []
      })
    });

    const responseText = await response.text();
    console.log('[TEST] Response status:', response.status);
    console.log('[TEST] Response body:', responseText);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${responseText}`);
    }

    return { success: true, response: responseText };
  } catch (error) {
    console.error('[TEST] Token generation failed:', error);
    return { success: false, error: error.message };
  }
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  return json({
    shop: session?.shop || 'No shop found',
    hasAccessToken: !!session?.accessToken,
    accessTokenPreview: session?.accessToken ? session.accessToken.substring(0, 20) + '...' : 'No access token'
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  if (!session?.shop || !session?.accessToken) {
    return json({ error: 'No valid session found' }, { status: 400 });
  }

  const result = await triggerStorefrontTokenGeneration(session.shop, session.accessToken);
  
  return json(result);
};

export default function TestToken() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>🧪 Storefront Token Test</h1>
      
      <div style={{ background: '#f5f5f5', padding: '15px', marginBottom: '20px' }}>
        <h3>Current Session Info:</h3>
        <p><strong>Shop:</strong> {loaderData.shop}</p>
        <p><strong>Has Access Token:</strong> {loaderData.hasAccessToken ? '✅ Yes' : '❌ No'}</p>
        <p><strong>Access Token Preview:</strong> {loaderData.accessTokenPreview}</p>
      </div>

      <Form method="post">
        <button 
          type="submit" 
          style={{ 
            padding: '10px 20px', 
            background: '#007cba', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          disabled={!loaderData.hasAccessToken}
        >
          🚀 Test Token Generation
        </button>
      </Form>

      {actionData && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          background: actionData.success ? '#d4edda' : '#f8d7da',
          border: `1px solid ${actionData.success ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px'
        }}>
          <h3>{actionData.success ? '✅ Success!' : '❌ Error'}</h3>
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {actionData.success ? actionData.response : actionData.error}
          </pre>
        </div>
      )}

      <div style={{ marginTop: '30px', fontSize: '12px', color: '#666' }}>
        <p><strong>What this test does:</strong></p>
        <ul>
          <li>Uses your current Shopify session's real access token</li>
          <li>Calls the backend worker to generate a Storefront token</li>
          <li>Shows the full request/response for debugging</li>
        </ul>
      </div>
    </div>
  );
}
