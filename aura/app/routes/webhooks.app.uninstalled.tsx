import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { authenticate, sessionStorage } from "../shopify.server";

/**
 * Trigger Storefront token cleanup after app uninstallation
 */
async function triggerStorefrontTokenCleanup(shop: string) {
  try {
    const backendWorkerUrl = process.env.BACKEND_WORKER_URL || 'https://aura-backend-worker.your-domain.workers.dev';

    const response = await fetch(`${backendWorkerUrl}/api/shopify/uninstall`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ shop })
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[UNINSTALL] Failed to cleanup Storefront tokens:', error);
      return false;
    }

    const result = await response.json();
    console.log('[UNINSTALL] Storefront token cleanup result:', result);
    return result.success;

  } catch (error: any) {
    console.error('[UNINSTALL] Error triggering Storefront token cleanup:', error);
    return false;
  }
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const { shop, session, topic } = await authenticate.webhook(request);

  console.log(`Received ${topic} webhook for ${shop}`);

  // Webhook requests can trigger multiple times and after an app has already been uninstalled.
  // If this webhook already ran, the session may have been deleted previously.
  if (session) {
    // Delete the session from KV storage
    await sessionStorage.deleteSession(session.id);
  }

  // Trigger Storefront token cleanup in background
  triggerStorefrontTokenCleanup(shop)
    .then(success => {
      if (success) {
        console.log('[UNINSTALL] ✅ Storefront tokens cleaned up successfully for:', shop);
      } else {
        console.error('[UNINSTALL] ❌ Failed to cleanup Storefront tokens for:', shop);
      }
    })
    .catch(error => {
      console.error('[UNINSTALL] ❌ Token cleanup error for:', shop, error);
    });

  return new Response();
};
