import { createShopifyApp } from "../shopify.server";
import type { CloudflareLoaderArgs } from "../types/cloudflare";

/**
 * Trigger Storefront token generation after successful OAuth
 */
async function triggerStorefrontTokenGeneration(shop: string, accessToken: string, env: any) {
  try {
    const backendWorkerUrl = env.BACKEND_WORKER_URL || 'https://aura-backend-worker.feisty-agency.workers.dev';

    const response = await fetch(`${backendWorkerUrl}/api/shopify/install`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shop,
        accessToken,
        scopes: env.SCOPES?.split(",") || []
      })
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[OAUTH] Failed to generate Storefront token:', error);
      return false;
    }

    const result = await response.json();
    console.log('[OAUTH] Storefront token generation result:', result);
    return result.success;

  } catch (error: any) {
    console.error('[OAUTH] Error triggering Storefront token generation:', error);
    return false;
  }
}

export const loader = async ({ request, context }: CloudflareLoaderArgs) => {
  // Create Shopify app instance with Cloudflare environment
  const shopify = createShopifyApp(context);
  const { session } = await shopify.authenticate.admin(request);

  // Trigger Storefront token generation after successful authentication
  if (session?.shop && session?.accessToken) {
    console.log('[OAUTH] App installed for shop:', session.shop);

    // Trigger token generation in background (don't block OAuth flow)
    triggerStorefrontTokenGeneration(session.shop, session.accessToken, context)
      .then(success => {
        if (success) {
          console.log('[OAUTH] ✅ Storefront token generated successfully for:', session.shop);
        } else {
          console.error('[OAUTH] ❌ Failed to generate Storefront token for:', session.shop);
        }
      })
      .catch(error => {
        console.error('[OAUTH] ❌ Token generation error for:', session.shop, error);
      });
  }

  return null;
};
