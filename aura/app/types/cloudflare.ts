// Cloudflare environment types for Workers runtime
export interface CloudflareEnv {
  SHOPIFY_SESSIONS: KVNamespace;
  SHOPIFY_API_KEY?: string;
  SHOPIFY_API_SECRET?: string;
  SCOPES?: string;
  SHOPIFY_APP_URL?: string;
  SHOP_CUSTOM_DOMAIN?: string;
  GEMINI_API_KEY?: string;
  BACKEND_WORKER_URL?: string;
}

// Extend LoaderFunctionArgs to include Cloudflare context
export interface CloudflareLoaderArgs {
  request: Request;
  params: any;
  context: CloudflareEnv;
}

// Global type augmentation for KVNamespace
declare global {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface KVNamespace {}
}
