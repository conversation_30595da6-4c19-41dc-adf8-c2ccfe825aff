import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  shopifyApp,
} from "@shopify/shopify-app-remix/server";
import { KVSessionStorage } from "@shopify/shopify-app-session-storage-kv";
import { MemorySessionStorage } from "@shopify/shopify-app-session-storage-memory";

// If you see a type error for KVNamespace, add this global declaration:
declare global {
  // Cloudflare KVNamespace type for session storage
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface KVNamespace {}
}

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new KVSessionStorage(process.env.SHOPIFY_SESSIONS),
  distribution: AppDistribution.AppStore,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: true,
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;

export function getSessionStorage(env: { SHOPIFY_SESSIONS: KVNamespace }) {
  return new KVSessionStorage(env.SHOPIFY_SESSIONS);
}

// NOTE: If you see a module not found error, run:
// npm install @shopify/shopify-app-session-storage-kv
