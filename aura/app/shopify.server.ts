import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  shopifyApp,
} from "@shopify/shopify-app-remix/server";
import { MemorySessionStorage } from "@shopify/shopify-app-session-storage-memory";
import { KVSessionStorage } from "@shopify/shopify-app-session-storage-kv";

// If you see a type error for KVNamespace, add this global declaration:
declare global {
  // Cloudflare KVNamespace type for session storage
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface KVNamespace {}
}

// Create session storage - use KV when available (wrangler dev), memory otherwise
function createSessionStorage() {
  // Check if we have access to KV namespace (when running with wrangler dev)
  if (typeof globalThis !== 'undefined' && (globalThis as any).SHOPIFY_SESSIONS) {
    console.log('✅ Using KV storage for sessions');
    return new KVSessionStorage((globalThis as any).SHOPIFY_SESSIONS);
  }

  // Fallback to memory storage for regular npm run dev
  console.log('⚠️ Using memory storage - sessions will not persist between restarts');
  console.log('💡 Run with "wrangler dev" for persistent KV session storage');
  return new MemorySessionStorage();
}

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: createSessionStorage(),
  distribution: AppDistribution.AppStore,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: true,
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;



// NOTE: If you see a module not found error, run:
// npm install @shopify/shopify-app-session-storage-kv
