import { RemixServer } from "@remix-run/react";
import { renderToString } from "react-dom/server";
import type { EntryContext } from "@remix-run/cloudflare";
import { createRequestHandler } from "@remix-run/cloudflare";
import * as build from "virtual:remix/server-build";

// Make KV namespace available globally for Shopify session storage
function setupGlobalKV(env: any) {
  if (env?.SHOPIFY_SESSIONS) {
    (globalThis as any).SHOPIFY_SESSIONS = env.SHOPIFY_SESSIONS;
  }
}

function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  loadContext: any
) {
  // Setup KV access
  setupGlobalKV(loadContext.env);
  // Handle health checks
  const url = new URL(request.url);
  
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS, POST",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept",
    "Access-Control-Max-Age": "86400",
  };
  
  if (request.method === 'OPTIONS' && (url.pathname.includes('/api/') || url.pathname.includes('/proxy/'))) {
    return new Response(null, { 
      status: 204, 
      headers: CORS_HEADERS 
    });
  }
  
  if (url.pathname === "/health" || url.pathname.includes("/health")) {
    return new Response(
      JSON.stringify({ 
        status: "ok",
        message: "Server is healthy",
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...CORS_HEADERS
        },
      }
    );
  }


  
  const markup = renderToString(
    <RemixServer context={remixContext} url={request.url} />
  );

  responseHeaders.set("Content-Type", "text/html");

  return new Response("<!DOCTYPE html>" + markup, {
    status: responseStatusCode,
    headers: responseHeaders,
  });
}

// Export the default Cloudflare Workers handler
export default {
  async fetch(request: Request, env: any, ctx: any) {
    // Setup KV access
    setupGlobalKV(env);

    // Create the Remix request handler
    const handler = createRequestHandler(build, "production");

    // Call the handler with the environment context
    return handler(request, { env, ctx });
  },
};
