import { RemixServer } from "@remix-run/react";
import { renderToString } from "react-dom/server";
import type { EntryContext } from "@remix-run/cloudflare";
import { addDocumentResponseHeaders } from "./shopify.server";

export default function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  loadContext: any
) {
  // Make Cloudflare environment available to routes through context
  const contextWithEnv = {
    ...loadContext,
    cloudflareEnv: loadContext.env || loadContext.cloudflareEnv,
  };
  // Handle health checks
  const url = new URL(request.url);
  
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS, POST",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept",
    "Access-Control-Max-Age": "86400",
  };
  
  if (request.method === 'OPTIONS' && (url.pathname.includes('/api/') || url.pathname.includes('/proxy/'))) {
    return new Response(null, { 
      status: 204, 
      headers: CORS_HEADERS 
    });
  }
  
  if (url.pathname === "/health" || url.pathname.includes("/health")) {
    return new Response(
      JSON.stringify({ 
        status: "ok",
        message: "Server is healthy",
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...CORS_HEADERS
        },
      }
    );
  }

  addDocumentResponseHeaders(request, responseHeaders);
  
  const markup = renderToString(
    <RemixServer context={remixContext} url={request.url} />
  );

  responseHeaders.set("Content-Type", "text/html");

  return new Response("<!DOCTYPE html>" + markup, {
    status: responseStatusCode,
    headers: responseHeaders,
  });
}
