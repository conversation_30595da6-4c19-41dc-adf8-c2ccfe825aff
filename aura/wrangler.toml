name = "aura-shopify-app"
main = "build/server/index.js"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# KV namespace for Shopify session storage
kv_namespaces = [
  { binding = "SHOPIFY_SESSIONS", id = "af659f092a1e406db03c0eb20b8bbf12" }
]

# Environment variables
[vars]
SHOPIFY_API_KEY = ""
SHOPIFY_API_SECRET = ""
SCOPES = "read_products,write_products,read_customers,write_customers"
SHOPIFY_APP_URL = "https://aura.fy.studio"
APP_PROXY_URL = "https://aura.fy.studio"
APP_PROXY_PATH = "/voice"
BACKEND_WORKER_URL = "https://aura-backend-worker.feisty-agency.workers.dev"
GEMINI_API_KEY = "AIzaSyCFzbQ7M4R0S7nssbku0hu4pxF7ybO48qg"

# Development configuration
[env.development]
kv_namespaces = [
  { binding = "SHOPIFY_SESSIONS", id = "af659f092a1e406db03c0eb20b8bbf12" }
]

# Production configuration  
[env.production]
kv_namespaces = [
  { binding = "SHOPIFY_SESSIONS", id = "af659f092a1e406db03c0eb20b8bbf12" }
]
