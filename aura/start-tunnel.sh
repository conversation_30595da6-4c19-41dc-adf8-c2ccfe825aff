#!/bin/bash

# Start Cloudflare tunnel for aura.fy.studio
echo "🚀 Starting Cloudflare tunnel for aura.fy.studio..."

# Check if cloudflared is installed
if ! command -v cloudflared &> /dev/null; then
    echo "❌ cloudflared is not installed. Please install it first:"
    echo "   brew install cloudflared"
    exit 1
fi

# Check if tunnel configuration exists
if [ ! -f "cloudflared-config.yml" ]; then
    echo "❌ cloudflared-config.yml not found"
    exit 1
fi

# Start the tunnel
echo "🔗 Starting tunnel with configuration..."
cloudflared tunnel --config cloudflared-config.yml run

echo "✅ Tunnel started successfully!"
echo "🌐 Your app should now be accessible at: https://aura.fy.studio"
