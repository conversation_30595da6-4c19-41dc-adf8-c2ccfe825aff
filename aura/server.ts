import { createRequestHandler } from "@remix-run/cloudflare";
import * as build from "./build/server/index.js";

// Make KV namespace available globally for Shopify session storage
function setupGlobalKV(env: any) {
  if (env?.SHOPIFY_SESSIONS) {
    (globalThis as any).SHOPIFY_SESSIONS = env.SHOPIFY_SESSIONS;
  }
}

export default {
  async fetch(request: Request, env: any, ctx: any) {
    // Setup KV access
    setupGlobalKV(env);

    // Handle health checks and CORS
    const url = new URL(request.url);

    const CORS_HEADERS = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS, POST",
      "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept",
      "Access-Control-Max-Age": "86400",
    };

    if (request.method === 'OPTIONS' && (url.pathname.includes('/api/') || url.pathname.includes('/proxy/'))) {
      return new Response(null, {
        status: 204,
        headers: CORS_HEADERS
      });
    }

    if (url.pathname === "/health" || url.pathname.includes("/health")) {
      return new Response(
        JSON.stringify({
          status: "ok",
          message: "Server is healthy",
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            ...CORS_HEADERS
          },
        }
      );
    }

    // Create the Remix request handler
    const handler = createRequestHandler({
      build,
      mode: "production",
      getLoadContext: () => ({ env, ctx }),
    });

    // Call the handler with the environment context
    return handler(request, { env, ctx });
  },
};
