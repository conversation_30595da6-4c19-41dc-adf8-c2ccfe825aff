{"name": "voice-ai-assistant", "private": true, "scripts": {"build": "remix vite:build", "build:assets": "rollup -c", "build:assets:prod": "NODE_ENV=production rollup -c", "build:assets:copy": "npm run build:assets && cp assets/voice-assistant-bundle.js extensions/voice-assistant/assets/voice-assistant-bundle.js", "build:assets:watch": "rollup -c --watch", "dev": "node scripts/dev-setup.js", "dev:simple": "npm run build:assets:copy && shopify app dev --path .", "dev:watch": "concurrently \"npm run build:assets:watch\" \"npm run copy:watch\" \"shopify app dev --path .\"", "copy:watch": "nodemon --watch assets/voice-assistant-bundle.js --exec \"cp assets/voice-assistant-bundle.js extensions/voice-assistant/assets/voice-assistant-bundle.js\"", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "graphql-codegen": "graphql-codegen", "vite": "vite", "start:livekit-proxy": "node scripts/start-livekit-proxy.js", "start:livekit-proxy:screen": "screen -dmS livekit-proxy node scripts/start-livekit-proxy.js", "start:livekit-proxy:docker": "docker-compose up -d livekit-proxy", "test:livekit-connection": "node scripts/test-livekit-connection.js", "setup:voice-assistant": "npm run start:livekit-proxy && npm run test:livekit-connection", "deploy:replicate": "./scripts/deploy-replicate.sh", "list": "node scripts/dev.js list", "parse-prd": "node scripts/dev.js parse-prd"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@cloudflare/realtimekit-react": "^0.0.0", "@livekit/components-react": "^2.8.1", "@livekit/components-styles": "^1.1.4", "@livekit/krisp-noise-filter": "^0.2.16", "@livekit/rtc-node": "^0.13.10", "@remix-run/dev": "^2.15.0", "@remix-run/fs-routes": "^2.15.0", "@remix-run/node": "^2.15.0", "@remix-run/react": "^2.15.0", "@remix-run/serve": "^2.15.0", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^12.0.0", "@shopify/polaris-icons": "^9.3.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-kv": "^1.0.0", "@shopify/shopify-app-session-storage-memory": "^4.0.18", "async-mutex": "^0.5.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "inquirer": "^12.5.0", "isbot": "^5.1.0", "jsonwebtoken": "^9.0.2", "libflacjs": "^5.4.0", "livekit-client": "^2.9.7", "livekit-server-sdk": "^2.10.2", "lru-cache": "^10.2.0", "node-fetch": "^3.3.2", "node-lame": "^1.3.2", "openai": "^4.89.0", "ora": "^8.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "replicate": "^1.0.1", "vite-tsconfig-paths": "^5.0.1", "ws": "^8.18.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.15.0", "@remix-run/route-config": "^2.15.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-url": "^8.0.2", "@rollup/plugin-wasm": "^6.2.2", "@sapphi-red/web-noise-suppressor": "^0.3.2", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "concurrently": "^9.1.2", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "nodemon": "^3.1.10", "prettier": "^3.2.4", "rollup": "^4.38.0", "typescript": "^5.2.2", "vite": "^5.1.3"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "overrides": {"@graphql-tools/url-loader": "8.0.16"}, "author": "wlvar"}