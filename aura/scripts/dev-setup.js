#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import chalk from 'chalk';

console.log(chalk.blue('🔧 Setting up development environment...'));

// Build and copy assets
console.log(chalk.yellow('📦 Building voice assistant assets...'));
try {
  execSync('npm run build:assets:copy', { stdio: 'inherit' });
  console.log(chalk.green('✅ Assets built and copied successfully'));
} catch (error) {
  console.error(chalk.red('❌ Failed to build assets:'), error.message);
  process.exit(1);
}

// Verify bundle exists in extension directory
const bundlePath = 'extensions/voice-assistant/assets/voice-assistant-bundle.js';
if (existsSync(bundlePath)) {
  console.log(chalk.green('✅ Bundle verified in extension directory'));
} else {
  console.error(chalk.red('❌ Bundle not found in extension directory'));
  process.exit(1);
}

console.log(chalk.blue('🚀 Development environment ready!'));
console.log(chalk.gray('Starting Shopify development server...'));

// Start Shopify dev server with custom tunnel URL
try {
  execSync('shopify app dev --path . --tunnel-url=https://aura.fy.studio', { stdio: 'inherit' });
} catch (error) {
  console.error(chalk.red('❌ Failed to start Shopify dev server:'), error.message);
  process.exit(1);
}