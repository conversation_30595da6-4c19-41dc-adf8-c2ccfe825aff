#voice-assistant {
  --assistant-color: attr(data-color);
  position: fixed;
  z-index: 999999;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.05);
  }
}

/* Processing state styles */
.va-text-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.voice-assistant-record.processing {
  opacity: 0.8;
  pointer-events: none;
}

.voice-assistant-record.processing span {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Default positioning - fallback to bottom-right if no data-position is set */
#voice-assistant {
  bottom: 20px;
  right: 20px;
}

#voice-assistant[data-position="bottom-right"] {
  bottom: 20px;
  right: 20px;
}

#voice-assistant[data-position="bottom-left"] {
  bottom: 20px;
  left: 20px;
}

#voice-assistant[data-position="top-right"] {
  top: 20px;
  right: 20px;
  bottom: auto;
}

#voice-assistant[data-position="top-left"] {
  top: 20px;
  left: 20px;
  bottom: auto;
}

/* Visualizer Styles with Glass Background */
.voice-assistant-visualizer-container {
  position: relative;
  width: 200px;
  height: 200px;
}

.voice-assistant-visualizer {
  transform: scale(0.5);
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

/* Glass Background Behind Visualizer Circle */
.voice-assistant-visualizer::before {
  content: '';
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(150%);
  -webkit-backdrop-filter: blur(20px) saturate(150%);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.2) inset,
    0 8px 32px rgba(139, 92, 246, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: -1;
}

/* iOS Safari fixes for visualizer glass background */
@supports (-webkit-backdrop-filter: blur(1px)) {
  .voice-assistant-visualizer::before {
    background: rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    backdrop-filter: blur(30px) saturate(180%);
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .voice-assistant-visualizer::before {
    background: rgba(255, 255, 255, 0.18);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }
}

.voice-assistant-visualizer:hover::before {
  transform: scale(1.02);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.3) inset,
    0 12px 48px rgba(139, 92, 246, 0.3),
    0 6px 24px rgba(0, 0, 0, 0.15);
}

#voice-assistant-canvas {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6, #6366f1, #ec4899);
  box-shadow: 0 0 50px rgba(139, 92, 246, 0.3);
  transition: transform 0.3s ease;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.voice-assistant-visualizer:hover #voice-assistant-canvas {
  transform: scale(1.05);
}

.voice-assistant-visualizer:active #voice-assistant-canvas {
  transform: scale(0.95);
}

.voice-assistant-loading {
  position: absolute;
  z-index: 2;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.164);
  backdrop-filter: blur(7px) saturate(120%);
  -webkit-backdrop-filter: blur(7px) saturate(120%);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  pointer-events: none; /* Allow clicks to pass when hidden */
}

/* Shopping bag icon overlay */
.voice-assistant-icon-overlay {
  position: absolute;
  z-index: 3!important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  pointer-events: none;
  transition: all 0.3s ease;
}

.voice-assistant-icon-overlay svg {
  display: block;
}

/* Organic cyber-cybernetic spinner */
.voice-assistant-spinner {
  position: relative;
  width: 120px;
  height: 120px;
  display: block;
}

/* Outer rotating ring with gradient */
.voice-assistant-spinner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    var(--assistant-color, #8b5cf6) 60deg,
    rgba(139, 92, 246, 0.8) 120deg,
    rgba(139, 92, 246, 0.4) 180deg,
    transparent 240deg,
    transparent 360deg
  );
  -webkit-mask: radial-gradient(circle at center, transparent 70%, black 75%, black 85%, transparent 90%);
  mask: radial-gradient(circle at center, transparent 70%, black 75%, black 85%, transparent 90%);
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6)) blur(0.5px);
  animation: va-spinner-rotate 3s cubic-bezier(0.4, 0.0, 0.2, 1) infinite;
}

/* Inner counter-rotating ring */
.voice-assistant-spinner::after {
  content: "";
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  border-radius: 50%;
  background: conic-gradient(
    from 180deg,
    transparent 0deg,
    rgba(139, 92, 246, 0.6) 90deg,
    var(--assistant-color, #8b5cf6) 150deg,
    rgba(139, 92, 246, 0.3) 240deg,
    transparent 360deg
  );
  -webkit-mask: radial-gradient(circle at center, transparent 60%, black 65%, black 80%, transparent 85%);
  mask: radial-gradient(circle at center, transparent 60%, black 65%, black 80%, transparent 85%);
  filter: drop-shadow(0 0 6px rgba(139, 92, 246, 0.4));
  animation: va-spinner-rotate 2s cubic-bezier(0.4, 0.0, 0.2, 1) infinite reverse;
}

/* Central pulsing core with particle effect */
.voice-assistant-spinner .vasp-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  background: radial-gradient(
    circle at center,
    var(--assistant-color, #8b5cf6) 0%,
    rgba(139, 92, 246, 0.8) 40%,
    rgba(139, 92, 246, 0.4) 70%,
    transparent 100%
  );
  border-radius: 50%;
  box-shadow:
    0 0 8px rgba(139, 92, 246, 1),
    0 0 16px rgba(139, 92, 246, 0.7),
    0 0 24px rgba(139, 92, 246, 0.4),
    inset 0 0 8px rgba(255, 255, 255, 0.3);
  animation: va-spinner-pulse 1.8s ease-in-out infinite;
}

/* Particle orbits */
.voice-assistant-spinner .vasp-dot::before,
.voice-assistant-spinner .vasp-dot::after {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--assistant-color, #8b5cf6);
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(139, 92, 246, 0.8);
}

.voice-assistant-spinner .vasp-dot::before {
  top: -20px;
  left: 50%;
  margin-left: -2px;
  animation: va-particle-orbit 2.4s linear infinite;
}

.voice-assistant-spinner .vasp-dot::after {
  top: -16px;
  left: 50%;
  margin-left: -2px;
  animation: va-particle-orbit 1.8s linear infinite reverse;
}

@keyframes va-spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes va-spinner-pulse {
  0%, 100% {
    transform: scale(0.85);
    opacity: 0.9;
    filter: brightness(1);
  }
  25% {
    transform: scale(1.1);
    opacity: 0.7;
    filter: brightness(1.2);
  }
  50% {
    transform: scale(1.25);
    opacity: 0.5;
    filter: brightness(1.4);
  }
  75% {
    transform: scale(1.1);
    opacity: 0.7;
    filter: brightness(1.2);
  }
}

@keyframes va-particle-orbit {
  0% {
    transform: rotate(0deg) translateX(20px) rotate(0deg);
    opacity: 1;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    transform: rotate(180deg) translateX(20px) rotate(-180deg);
    opacity: 0.3;
  }
  75% {
    opacity: 0.6;
  }
  100% {
    transform: rotate(360deg) translateX(20px) rotate(-360deg);
    opacity: 1;
  }
}

.voice-assistant-loading.hidden {
  display: none;
}

.voice-assistant-spinner {
  display: block!important;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(139, 92, 246, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Mic Button with Glass Effect */
.voice-assistant-floating-mic {
  position: absolute;
  top: 36%;
  right: 36%;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.2) inset,
    0 8px 32px rgba(139, 92, 246, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
}

.voice-assistant-floating-mic:hover {
  transform: scale(1.05);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.3) inset,
    0 12px 48px rgba(139, 92, 246, 0.3),
    0 6px 24px rgba(0, 0, 0, 0.15);
}

.voice-assistant-floating-mic:active {
  transform: scale(0.95);
}

.voice-assistant-floating-mic.recording {
  background: rgba(239, 68, 68, 0.2);
  box-shadow: 
    0 0 0 1px rgba(239, 68, 68, 0.3) inset,
    0 8px 32px rgba(239, 68, 68, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.1);
  animation: recordingPulse 1.5s infinite;
}

@keyframes recordingPulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* Chat Bubble with Glass Effect */
.voice-assistant-chat-bubble-container {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  white-space: normal;
  z-index: 5;
  max-width: 320px;
  width: max-content;
}

/* Integrated product cards within chat message */
.chat-bubble-products {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
  max-width: 280px;
  scrollbar-width: none; /* Firefox */
}

.chat-bubble-products::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.chat-bubble-products::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.voice-assistant-chat-bubble {
  position: absolute;
  top: -100%;
  left: 50%;
  transform: translateX(-50%)!important;
  max-width: 280px;
  min-width: 200px;
  background: rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(15px) saturate(140%);
  backdrop-filter: blur(15px) saturate(140%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 16px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  text-align: center;
  z-index: 3;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

/* iOS Safari-specific fixes for glass effects */
@supports (-webkit-backdrop-filter: blur(1px)) {
  .voice-assistant-chat-bubble {
    background: rgba(255, 255, 255, 0.42);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
    backdrop-filter: blur(20px) saturate(150%);
  }
}

/* Additional iOS Safari fallback */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .voice-assistant-chat-bubble {
    background: rgba(255, 255, 255, 0.48);
    border: 1.5px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
      0 0 0 1px rgba(255, 255, 255, 0.2) inset,
      0 12px 48px rgba(0, 0, 0, 0.3),
      0 6px 24px rgba(0, 0, 0, 0.2);
  }
}

#voice-assistant-chat-message {
  color: rgba(55, 65, 81, 0.9);
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-bubble-text {
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
  text-shadow: inherit;
}

/* Assistant Name Display with Glass Effect */
.voice-assistant-name-display {
  position: absolute;
  bottom: -100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;
}

#voice-assistant-name {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px 16px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  white-space: nowrap;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Modal with Glass Effect */
.voice-assistant-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 420px;
  max-width: 90vw;
  height: 500px;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(120%);
  -webkit-backdrop-filter: blur(20px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 24px 80px rgba(0, 0, 0, 0.3),
    0 12px 40px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.voice-assistant-modal.open {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

.voice-assistant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.voice-assistant-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.voice-assistant-close {
  background: none;
  border: none;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 4px;
  border-radius: 8px;
  transition: all 0.2s ease;
  line-height: 1;
}

.voice-assistant-close:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
}

.voice-assistant-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.voice-assistant-messages {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
}

.voice-assistant-messages::-webkit-scrollbar {
  width: 6px;
}

.voice-assistant-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.voice-assistant-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.voice-assistant-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Message Bubbles with Glass Effect */
.message {
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 85%;
  word-wrap: break-word;
  line-height: 1.4;
  font-size: 14px;
  position: relative;
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  align-self: flex-end;
  background: rgba(139, 92, 246, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(139, 92, 246, 0.3);
}

.message.assistant {
  align-self: flex-start;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

.message:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 6px 24px rgba(0, 0, 0, 0.15);
}

/* Controls with Glass Effect */
.voice-assistant-controls {
  padding: 20px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.voice-assistant-record {
  width: 100%;
  padding: 16px 20px;
  border: none;
  border-radius: 12px;
  background: rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 
    0 0 0 1px rgba(139, 92, 246, 0.1) inset,
    0 4px 16px rgba(139, 92, 246, 0.2);
}

.voice-assistant-record:hover {
  transform: translateY(-2px);
  background: rgba(139, 92, 246, 0.25);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 
    0 0 0 1px rgba(139, 92, 246, 0.2) inset,
    0 8px 32px rgba(139, 92, 246, 0.3);
}

.voice-assistant-record:active {
  transform: translateY(0);
}

.voice-assistant-record.recording {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 
    0 0 0 1px rgba(239, 68, 68, 0.1) inset,
    0 4px 16px rgba(239, 68, 68, 0.2);
  animation: recordingButtonPulse 1.5s infinite;
}

@keyframes recordingButtonPulse {
  0% { box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1) inset, 0 4px 16px rgba(239, 68, 68, 0.2); }
  50% { box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.2) inset, 0 8px 32px rgba(239, 68, 68, 0.4); }
  100% { box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1) inset, 0 4px 16px rgba(239, 68, 68, 0.2); }
}

.voice-assistant-record svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Glass Modal Backdrop */
.voice-assistant-modal::before {
  content: '';
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.voice-assistant-modal.open::before {
  opacity: 1;
}

/* Glass Product Recommendations */
.glass-product-recommendations {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-top: 12px;
  padding: 16px;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 4px 16px rgba(0, 0, 0, 0.1);
}

.glass-product-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 4px 16px rgba(0, 0, 0, 0.1);
}

.glass-product-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 8px 32px rgba(0, 0, 0, 0.15);
}

.glass-product-card img {
  width: 100%;
  height: 140px;
  object-fit: cover;
}

.glass-product-card h3,
.glass-product-card p {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.chat-bubble-products {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
  max-width: 280px;
  scrollbar-width: none; /* Firefox */
}

.chat-bubble-products::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.chat-bubble-products::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.chat-bubble-product-card {
  width: 140px;
  flex: 0 0 auto;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(12px) saturate(120%);
  -webkit-backdrop-filter: blur(12px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.chat-bubble-product-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.35);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.15) inset,
    0 12px 32px rgba(0, 0, 0, 0.2),
    0 6px 24px rgba(139, 92, 246, 0.15);
}

.chat-bubble-product-card:focus {
  outline: 2px solid rgba(139, 92, 246, 0.6);
  outline-offset: 2px;
}

/* Product Image Container */
.product-image-container {
  position: relative;
  width: 100%;
  height: 100px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.chat-bubble-product-card:hover .product-image {
  transform: scale(1.05);
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.4);
}

.product-sale-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: #ef4444;
  color: white;
  font-size: 8px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
  text-shadow: none;
  z-index: 2;
}

/* Product Details */
.product-details {
  padding: 8px 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-title {
  color: rgba(255, 255, 255, 0.95);
  font-size: 11px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin: 0;
  min-height: 26px;
}

.product-price-container {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.product-price {
  color: rgba(139, 92, 246, 0.95);
  font-size: 12px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0;
  line-height: 1;
}

.product-compare-price {
  color: rgba(255, 255, 255, 0.5);
  font-size: 10px;
  font-weight: 500;
  text-decoration: line-through;
  margin: 0;
  line-height: 1;
}

.product-variant-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 9px;
  font-weight: 500;
  margin: 0;
}

/* Product Rating */
.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.product-stars {
  display: flex;
  gap: 1px;
}

.product-stars span {
  font-size: 10px;
  line-height: 1;
}

.star-filled {
  color: #fbbf24;
}

.star-half {
  color: #fbbf24;
}

.star-empty {
  color: rgba(255, 255, 255, 0.3);
}

.review-count {
  color: rgba(255, 255, 255, 0.6);
  font-size: 8px;
  font-weight: 500;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 6px;
  padding: 6px 10px 10px;
  margin-top: auto;
}

.product-add-to-cart,
.product-quick-view {
  flex: 1;
  height: 28px;
  border: none;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  position: relative;
  overflow: hidden;
}

.product-add-to-cart {
  background: rgba(139, 92, 246, 0.9);
  color: white;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.product-add-to-cart:hover {
  background: rgba(139, 92, 246, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.product-add-to-cart.loading {
  background: rgba(139, 92, 246, 0.5);
  cursor: not-allowed;
}

.product-add-to-cart.loading::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.product-quick-view {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-quick-view:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.product-add-to-cart svg,
.product-quick-view svg {
  width: 12px;
  height: 12px;
}

/* Quick View Modal */
.product-quick-view-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-quick-view-overlay.visible {
  opacity: 1;
}

.product-quick-view-modal {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(120%);
  -webkit-backdrop-filter: blur(20px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  color: rgba(255, 255, 255, 0.95);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.product-quick-view-overlay.visible .product-quick-view-modal {
  transform: scale(1);
}

.quick-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-view-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.quick-view-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.quick-view-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.quick-view-content {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.quick-view-image {
  flex: 0 0 200px;
}

.quick-view-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.quick-view-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-view-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-view-price .price {
  font-size: 20px;
  font-weight: 700;
  color: rgba(139, 92, 246, 0.95);
}

.quick-view-price .compare-price {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  text-decoration: line-through;
}

.quick-view-description {
  font-size: 14px;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.8);
}

.quick-view-actions {
  display: flex;
  gap: 12px;
  margin-top: auto;
}

.quick-view-add-to-cart,
.quick-view-full-details {
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.quick-view-add-to-cart {
  background: rgba(139, 92, 246, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: white;
  flex: 1;
}

.quick-view-add-to-cart:hover {
  background: rgba(139, 92, 246, 1);
  transform: translateY(-1px);
}

.quick-view-full-details {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.quick-view-full-details:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Glass Chat Bubbles */
.glass-chat-bubble {
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 4px 16px rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.glass-chat-user {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.glass-chat-assistant {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* iOS Safari specific fixes */
.ios-safari .voice-assistant-chat-bubble {
  background: rgba(255, 255, 255, 0.3) !important;
  -webkit-backdrop-filter: blur(30px) saturate(250%) !important;
  backdrop-filter: blur(30px) saturate(250%) !important;
  border: 2px solid rgba(255, 255, 255, 0.5) !important;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.3) inset,
    0 20px 80px rgba(0, 0, 0, 0.5),
    0 10px 40px rgba(0, 0, 0, 0.3) !important;
}

.ios-safari .voice-assistant-visualizer::before {
  background: rgba(255, 255, 255, 0.25) !important;
  -webkit-backdrop-filter: blur(40px) saturate(250%) !important;
  backdrop-filter: blur(40px) saturate(250%) !important;
  border: 2px solid rgba(255, 255, 255, 0.4) !important;
}

.ios-safari #voice-assistant-chat-message {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.ios-safari .voice-assistant-modal {
  background: rgba(255, 255, 255, 0.25) !important;
  -webkit-backdrop-filter: blur(40px) saturate(300%) !important;
  backdrop-filter: blur(40px) saturate(300%) !important;
  border: 2px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.25) inset,
    0 32px 120px rgba(0, 0, 0, 0.6),
    0 16px 60px rgba(0, 0, 0, 0.4) !important;
}

.ios-safari .voice-assistant-header {
  background: rgba(255, 255, 255, 0.15) !important;
  -webkit-backdrop-filter: blur(20px) saturate(200%) !important;
  backdrop-filter: blur(20px) saturate(200%) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.25) !important;
}

.ios-safari .voice-assistant-controls {
  background: rgba(255, 255, 255, 0.15) !important;
  -webkit-backdrop-filter: blur(20px) saturate(200%) !important;
  backdrop-filter: blur(20px) saturate(200%) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.25) !important;
}

.ios-safari .message.assistant {
  background: rgba(255, 255, 255, 0.2) !important;
  -webkit-backdrop-filter: blur(15px) saturate(200%) !important;
  backdrop-filter: blur(15px) saturate(200%) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.ios-safari .message.user {
  background: rgba(139, 92, 246, 0.3) !important;
  -webkit-backdrop-filter: blur(15px) saturate(200%) !important;
  backdrop-filter: blur(15px) saturate(200%) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
}

@media screen and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 1) {
  /* Target iOS Safari specifically */
  .voice-assistant-chat-bubble {
    background: rgba(255, 255, 255, 0.45) !important;
    -webkit-backdrop-filter: blur(25px) saturate(200%) !important;
    backdrop-filter: blur(25px) saturate(200%) !important;
    border: 2px solid rgba(255, 255, 255, 0.4) !important;
    box-shadow: 
      0 0 0 1px rgba(255, 255, 255, 0.25) inset,
      0 16px 64px rgba(0, 0, 0, 0.4),
      0 8px 32px rgba(0, 0, 0, 0.25) !important;
  }
  
  .voice-assistant-visualizer::before {
    background: rgba(255, 255, 255, 0.22) !important;
    -webkit-backdrop-filter: blur(35px) saturate(200%) !important;
    backdrop-filter: blur(35px) saturate(200%) !important;
    border: 1px solid rgba(255, 255, 255, 0.35) !important;
  }
}

/* Expanded bubble with inline controls */
.voice-assistant-chat-bubble.expanded {
  max-width: 450px;
  min-width: 380px;
  padding: 20px;
  border-radius: 20px;
  transform: translateY(0) scale(1.05) !important;
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.15) inset,
    0 16px 64px rgba(0, 0, 0, 0.2),
    0 8px 32px rgba(139, 92, 246, 0.25);
  backdrop-filter: blur(25px) saturate(150%);
  -webkit-backdrop-filter: blur(25px) saturate(150%);
}

/* Bubble inline controls */
.bubble-inline-controls {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Bubble messages container */
.bubble-messages-container {
  max-height: 200px;
  margin-bottom: 12px;
}

.bubble-messages-list {
  max-height: 180px;
  overflow-y: auto;
  padding: 8px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.bubble-messages-list::-webkit-scrollbar {
  width: 4px;
}

.bubble-messages-list::-webkit-scrollbar-track {
  background: transparent;
}

.bubble-messages-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.bubble-messages-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

.bubble-message {
  margin-bottom: 8px;
  animation: bubbleMessageSlideIn 0.3s ease-out;
}

@keyframes bubbleMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bubble-message-content {
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 12px;
  line-height: 1.3;
  max-width: 90%;
}

.bubble-message-user .bubble-message-content {
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.3);
  margin-left: auto;
  text-align: right;
}

.bubble-message-assistant .bubble-message-content {
  background: rgba(255, 255, 255, 0.45);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bubble-message-text {
  color: rgba(255, 255, 255, 0.9);
  display: block;
}

.bubble-products-container {
  display: flex;
  gap: 6px;
  margin-top: 6px;
  overflow-x: auto;
  padding: 4px 0;
}

.bubble-mini-product-card {
  min-width: 80px;
  max-width: 80px;
  height: 100px;
  flex: 0 0 auto;
  font-size: 8px;
}

.bubble-mini-product-card .product-image {
  height: 50px;
}

.bubble-mini-product-card .product-info {
  padding: 4px;
}

.bubble-mini-product-card .product-title {
  font-size: 8px;
  line-height: 1.2;
  margin-bottom: 2px;
}

.bubble-mini-product-card .product-price {
  font-size: 8px;
  font-weight: 600;
}

.bubble-control-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.bubble-record-btn {
  flex: 0 0 auto;
  width: 36px;
  height: 36px;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.bubble-record-btn:hover {
  background: rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.4);
  transform: scale(1.05);
}

.bubble-record-btn.recording {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  animation: bubbleRecordPulse 1.5s infinite;
}

@keyframes bubbleRecordPulse {
  0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.1); }
  100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

.bubble-text-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  padding: 4px 4px 4px 12px;
  transition: all 0.2s ease;
}

.bubble-text-input-container:focus-within {
  border-color: rgba(139, 92, 246, 0.4);
  background: rgba(255, 255, 255, 0.45);
}

.bubble-text-input {
  flex: 1;
  background: none;
  border: none;
  outline: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  padding: 8px 0;
}

.bubble-text-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.bubble-send-btn {
  width: 28px;
  height: 28px;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.bubble-send-btn:hover {
  background: rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.4);
  transform: scale(1.05);
}

.bubble-status-indicator {
  height: 2px;
  background: rgba(139, 92, 246, 0.3);
  border-radius: 1px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.bubble-status-indicator.active {
  opacity: 1;
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% { background: rgba(139, 92, 246, 0.3); }
  50% { background: rgba(139, 92, 246, 0.6); }
}

/* Enhanced loading status with tool indicators */
.bubble-loading-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin: 4px 0;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.bubble-loading-status.active {
  opacity: 1;
  transform: translateY(0);
}

.bubble-loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid rgba(139, 92, 246, 0.8);
  border-radius: 50%;
  animation: bubbleSpinner 1s linear infinite;
}

@keyframes bubbleSpinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bubble-loading-text {
  flex: 1;
  line-height: 1.2;
}

.bubble-loading-dots {
  display: flex;
  gap: 2px;
}

.bubble-loading-dot {
  width: 3px;
  height: 3px;
  background: rgba(139, 92, 246, 0.6);
  border-radius: 50%;
  animation: bubbleDotPulse 1.5s infinite;
}

.bubble-loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.bubble-loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bubbleDotPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Tool execution indicators */
.bubble-tool-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  margin: 2px 0;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 8px;
  font-size: 10px;
  color: rgba(34, 197, 94, 0.9);
  animation: bubbleToolSlideIn 0.3s ease-out;
}

@keyframes bubbleToolSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.bubble-tool-icon {
  width: 10px;
  height: 10px;
  background: rgba(34, 197, 94, 0.3);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
}

.bubble-tool-text {
  flex: 1;
  font-weight: 500;
}

/* Utility Classes for Glass Effects */
.glass-surface {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1);
}

.glass-surface-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .voice-assistant-modal {
    width: 95vw;
    height: 90vh;
    max-height: 90vh;
  }
  
  .voice-assistant-visualizer-container {
    width: 200px;
    height: 200px;
  }
  
  .voice-assistant-chat-bubble-container {
    top: -100px;
    max-width: 280px;
  }
  
  #voice-assistant-canvas {
    width: 100%;
    height: 100%;
  }
  
  .voice-assistant-floating-mic {
    width: 48px;
    height: 48px;
    top: -12px;
    right: -12px;
  }
  
  .voice-assistant-floating-mic svg {
    width: 24px;
    height: 24px;
  }
  
  .voice-assistant-messages {
    padding: 16px 20px;
  }
  
  .voice-assistant-controls {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .voice-assistant-visualizer-container {
    width: 200px;
    height: 200px;
  }
  
  .voice-assistant-chat-bubble-container {
    top: -80px;
    max-width: 260px;
  }
  
  .voice-assistant-modal {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  
  .voice-assistant-modal.open {
    transform: translate(-50%, -50%) scale(1);
  }
}

/*
 * Virtual Cursor for Automation
 * --------------------------------------------------
 */
.voice-assistant-virtual-cursor {
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  background-color: rgba(99, 102, 241, 0.8); /* Indigo-500 */
  border: 2px solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 99999;
  transition: opacity 0.3s ease-out, transform 0.1s linear;
  opacity: 0;
}

.voice-assistant-virtual-cursor .ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: rgba(99, 102, 241, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple-animation 0.5s ease-out;
}

@keyframes ripple-animation {
  from {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  to {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Text input form positioning and styling */
.va-text-input-form {
  position: absolute;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
  width: 280px;
  z-index: 5;
}

.va-text-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(15px) saturate(140%);
  backdrop-filter: blur(15px) saturate(140%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 24px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 14px;
  font-weight: 400;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.va-text-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.va-text-input:focus {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.15) inset,
    0 0 0 2px rgba(139, 92, 246, 0.3),
    0 8px 24px rgba(0, 0, 0, 0.3);
}

.va-text-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}